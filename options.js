// options.js - 简易设置读写到 chrome.storage

const defaults = { fontSize: 16, theme: 'light', width: 65 };

async function loadOptions() {
  const result = await chrome.storage.local.get(['fontSize', 'theme', 'width']);
  document.getElementById('fontSize').value = result.fontSize ?? defaults.fontSize;
  document.getElementById('theme').value = result.theme ?? defaults.theme;
  document.getElementById('width').value = result.width ?? defaults.width;
}

async function saveOptions() {
  const fontSize = Math.max(12, Math.min(24, Number(document.getElementById('fontSize').value)));
  const width = Math.max(40, Math.min(100, Number(document.getElementById('width').value)));
  const theme = document.getElementById('theme').value;
  await chrome.storage.local.set({ fontSize, theme, width });
  alert('已保存设置');
}

document.getElementById('save').addEventListener('click', saveOptions);

document.addEventListener('DOMContentLoaded', loadOptions);

