// popup.js

document.getElementById('toggle').addEventListener('click', async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (!tab?.id) return;

  // 检查是否是特殊页面（chrome://、extension://等）
  if (tab.url?.startsWith('chrome://') || tab.url?.startsWith('chrome-extension://') || tab.url?.startsWith('moz-extension://')) {
    alert('无法在此类页面启用阅读模式');
    return;
  }

  try {
    // 先尝试发送切换消息
    await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
    window.close();
  } catch (e) {
    // 若失败，显式注入后再发送
    try {
      console.log('首次注入content script...');
      await chrome.scripting.executeScript({ target: { tabId: tab.id }, files: ['content.js'] });
      await chrome.scripting.insertCSS({ target: { tabId: tab.id }, files: ['content.css'] });

      // 等待一小段时间确保注入完成
      await new Promise(resolve => setTimeout(resolve, 100));

      await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
      window.close();
    } catch (err) {
      console.warn('注入失败:', err);
      alert('无法在此页面启用阅读模式\n可能原因：页面限制或权限不足');
    }
  }
});

