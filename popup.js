// popup.js

document.getElementById('toggle').addEventListener('click', async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (!tab?.id) return;

  // 检查是否是特殊页面（chrome://、extension://等）
  if (tab.url?.startsWith('chrome://') || tab.url?.startsWith('chrome-extension://') || tab.url?.startsWith('moz-extension://')) {
    alert('无法在此类页面启用阅读模式');
    return;
  }

  try {
    // 先尝试发送切换消息
    console.log('[阅读助手] 尝试发送切换消息到标签页:', tab.id);
    const response = await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
    console.log('[阅读助手] 消息发送成功，响应:', response);
    window.close();
  } catch (e) {
    console.log('[阅读助手] 首次消息发送失败:', e.message);
    // 若失败，显式注入后再发送
    try {
      console.log('首次注入content script...');
      await chrome.scripting.executeScript({ target: { tabId: tab.id }, files: ['content.js'] });
      await chrome.scripting.insertCSS({ target: { tabId: tab.id }, files: ['content.css'] });

      // 增加等待时间，确保注入和初始化完成
      await new Promise(resolve => setTimeout(resolve, 300));

      // 多次重试发送消息，确保成功
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
          window.close();
          return; // 成功则退出
        } catch (retryErr) {
          retryCount++;
          console.warn(`消息发送重试 ${retryCount}/${maxRetries}:`, retryErr);
          if (retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        }
      }

      // 所有重试都失败
      throw new Error('消息发送失败，已重试' + maxRetries + '次');

    } catch (err) {
      console.warn('注入或消息发送失败:', err);
      alert('无法在此页面启用阅读模式\n可能原因：页面限制或权限不足');
    }
  }
});

