// popup.js

document.getElementById('toggle').addEventListener('click', async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (!tab?.id) return;
  try {
    // 先尝试发送切换消息（若content已在匹配清单中运行则可立即响应）
    await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
    window.close();
  } catch (e) {
    // 若失败，显式注入后再发送，确保首次点击也能生效
    try {
      await chrome.scripting.executeScript({ target: { tabId: tab.id }, files: ['content.js'] });
      await chrome.scripting.insertCSS({ target: { tabId: tab.id }, files: ['content.css'] });
      await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
      window.close();
    } catch (err) {
      console.warn('注入失败', err);
      alert('无法在此页面启用阅读模式');
    }
  }
});

