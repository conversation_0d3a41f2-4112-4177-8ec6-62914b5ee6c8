// popup.js

document.getElementById('toggle').addEventListener('click', async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (!tab?.id) return;
  try {
    await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
    window.close();
  } catch (e) {
    console.warn('发送消息失败，尝试注入脚本后再试', e);
    try {
      await chrome.scripting.executeScript({ target: { tabId: tab.id }, files: ['content.js'] });
      await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
      window.close();
    } catch (err) {
      alert('无法在此页面启用阅读模式');
    }
  }
});

