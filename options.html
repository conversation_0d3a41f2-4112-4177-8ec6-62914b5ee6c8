<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>阅读助手设置</title>
    <style>
      body { font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Arial,'Noto Sans SC','PingFang SC',sans-serif; margin: 16px; }
      label { display:block; margin: 8px 0 4px; }
      input, select { padding: 6px 8px; border:1px solid #e5e7eb; border-radius:6px; }
      .row { display:flex; gap:12px; align-items:center; }
      .save { margin-top: 12px; padding: 8px 12px; border:1px solid #e5e7eb; border-radius:6px; background:#fff; cursor:pointer; }
    </style>
  </head>
  <body>
    <h1>阅读助手 - 设置</h1>
    <div class="row">
      <div>
        <label for="fontSize">字号（12-24）</label>
        <input type="number" id="fontSize" min="12" max="24" step="1" />
      </div>
      <div>
        <label for="theme">主题</label>
        <select id="theme">
          <option value="light">默认</option>
          <option value="dark">夜间</option>
          <option value="sepia">纸张</option>
        </select>
      </div>
      <div>
        <label for="width">内容宽度（40-100ch）</label>
        <input type="number" id="width" min="40" max="100" step="1" />
      </div>
    </div>
    <button class="save" id="save">保存</button>

    <script src="options.js"></script>
  </body>
</html>

