<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读助手测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; margin-bottom: 20px; }
        .sidebar { background: #e0e0e0; padding: 15px; margin: 10px 0; }
        .content { background: #fff; padding: 20px; }
        .ad { background: #ffcccc; padding: 10px; margin: 10px 0; border: 1px solid #ff9999; }
        .footer { background: #f0f0f0; padding: 20px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>网站头部导航</h1>
        <nav>首页 | 新闻 | 体育 | 娱乐</nav>
    </div>

    <div class="sidebar">
        <h3>侧边栏</h3>
        <p>这是侧边栏内容，不应该被提取到阅读模式中。</p>
    </div>

    <div class="ad">
        <strong>广告内容</strong>
        <p>这是一个广告区域，不应该出现在阅读模式中。</p>
    </div>

    <article class="content">
        <h1>这是文章标题</h1>
        <p>这是文章的第一段内容。这段文字应该被正确提取到阅读模式中，因为它是主要内容。这段内容有足够的长度来测试视觉特征判断算法的准确性。</p>

        <p>这是文章的第二段内容。我们需要确保这些段落能够被正确识别和提取。阅读助手应该能够识别出这些是正文内容，而不是评论或广告内容。</p>

        <h2>这是一个子标题</h2>
        <p>子标题下的内容也应该被包含在内。这里有更多的文字来测试内容提取的准确性。正文内容通常具有合理的字体大小、行高和段落长度。</p>

        <p>这是另一个段落，用来测试滚动功能。当内容足够长时，应该能够在阅读模式中正常滚动浏览。滚动应该是流畅的，不会出现双重滚动条的问题。</p>

        <p>继续添加更多内容来测试滚动功能。这些段落应该都能在阅读模式中正常显示和滚动。每个段落都有足够的内容来触发视觉特征判断。</p>

        <p>更多测试内容。我们需要确保所有的正文内容都能被正确提取，而广告、导航、侧边栏等内容被过滤掉。新的算法会考虑字体大小、位置、宽度等视觉特征。</p>

        <p>测试关闭功能的段落。点击右上角的关闭按钮或按ESC键都应该能够立即关闭阅读模式。关闭功能现在使用了更可靠的事件绑定机制。</p>

        <p>测试首次点击功能的段落。在新页面第一次点击"开启阅读模式"按钮应该立即生效，不需要点击第二次。这个问题已经通过改进消息监听器机制解决。</p>

        <p>最后一段测试内容，用来确保内容足够长以测试滚动功能。这段内容应该能够在阅读模式中正常显示，并且整个页面应该只有一个滚动条，不会出现iframe内部的第二个滚动条。</p>
    </article>

    <div class="comments" style="margin-top: 30px; padding: 20px; background: #f5f5f5; font-size: 12px;">
        <h3>评论区</h3>
        <div class="comment">
            <p><a href="#">用户1</a>: 这是一条评论，应该被过滤掉，不出现在阅读模式中。</p>
        </div>
        <div class="comment">
            <p><a href="#">用户2</a>: 另一条评论，链接密度高，字体较小。</p>
        </div>
    </div>

    <div class="ad">
        <strong>另一个广告</strong>
        <p>这也是广告内容，应该被过滤掉。</p>
    </div>

    <div class="footer">
        <p>网站页脚信息，版权声明等，不应该出现在阅读模式中。</p>
    </div>
</body>
</html>
