// content.js - 在页面中注入阅读器覆盖层，并进行启发式内容提取

(function () {
  const OVERLAY_ID = 'reader-overlay-container';

  // 读取用户设置以应用到阅读器（顶层作用域，便于复用）
  async function getUserSettings() {
    return new Promise((resolve) => {
      try {
        chrome.storage?.local.get(['fontSize','theme','width'], (res) => {
          resolve({
            fontSize: res.fontSize ?? 16,
            theme: res.theme ?? 'light',
            width: res.width ?? 65,
          });
        });
      } catch (e) {
        resolve({ fontSize: 16, theme: 'light', width: 65 });
      }
    });
  }

  // 创建覆盖层（包含一个iframe以隔离样式）
  function ensureOverlay() {
    let container = document.getElementById(OVERLAY_ID);

    if (container) return container;

    container = document.createElement('div');
    container.id = OVERLAY_ID;

    const backdrop = document.createElement('div');
    backdrop.id = 'reader-overlay-backdrop';
    backdrop.addEventListener('click', () => toggleOverlay(false));

    const overlay = document.createElement('div');
    overlay.id = 'reader-overlay';

    const iframe = document.createElement('iframe');
    iframe.setAttribute('title', '阅读模式');
    iframe.setAttribute('aria-label', '阅读模式');

    overlay.appendChild(iframe);
    container.appendChild(backdrop);
    container.appendChild(overlay);

    document.documentElement.appendChild(container);

    // 初始化iframe文档
    iframe.addEventListener('load', () => {
      const doc = iframe.contentDocument;
      if (!doc) return;
      doc.open();
      doc.write(`<!doctype html><html lang="zh-CN"><head><meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>阅读模式</title>
        <style>
          :root {
            --bg-primary: #ffffff; --bg-secondary: #f9fafb; --text-primary: #1f2937; --text-secondary: #6b7280; --border-color: #e5e7eb;
          }
          html, body { height: 100%; margin: 0; }
          body { background: var(--bg-primary); color: var(--text-primary); font-family: -apple-system,BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; }
          .reader-header { display:flex; justify-content:space-between; align-items:center; padding: 12px 20px; border-bottom: 1px solid var(--border-color); background: var(--bg-secondary); }
          .reader-title { margin: 0; font-size: 20px; font-weight: 600; }
          .reader-close { background: none; border: none; font-size: 22px; cursor: pointer; color: var(--text-secondary); padding: 4px 8px; border-radius: 6px; }
          .reader-close:hover { background: #f3f4f6; color: var(--text-primary); }
          .reader-content { height: calc(100% - 52px); overflow: auto; padding: 24px; line-height: 1.7; max-width: 70ch; margin: 0 auto; }
          .reader-content img { max-width: 100%; height: auto; }
          .sr-only { position:absolute; width:1px; height:1px; padding:0; margin:-1px; overflow:hidden; clip:rect(0,0,0,0); border:0; }
        </style>
      </head><body>
        <header class="reader-header">
          <h1 class="reader-title" id="reader-title"></h1>
          <button class="reader-close" aria-label="关闭" title="关闭 (Esc)">×</button>
        </header>
        <main class="reader-content" role="main" id="reader-content"></main>
        <div class="sr-only" aria-live="polite" id="reader-status"></div>
        <script>
          // 绑定关闭
          document.querySelector('.reader-close').addEventListener('click', () => parent.postMessage({ __reader_close: true }, '*'));
          // 键盘快捷键
          document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') { parent.postMessage({ __reader_close: true }, '*'); }
          });
        <\/script>
      </body></html>`);
      doc.close();
    });

    return container;
  }

  function toggleOverlay(show) {
    const container = ensureOverlay();
    container.style.display = show ? 'block' : 'none';
  }

    // 应用主题（简化实现）
    const themeMap = {
      light: { bg: '#ffffff', text: '#1f2937', border: '#e5e7eb', secondary: '#f9fafb' },
      dark: { bg: '#1f2937', text: '#f9fafb', border: '#4b5563', secondary: '#374151' },
      sepia: { bg: '#f7f3e9', text: '#5d4e37', border: '#d4c4a8', secondary: '#f0e6d2' },
    };

  // 简单启发式：从页面中找出主要内容容器
  function extractMainContent() {
    const selectors = [
      'article', '[role="main"]', '.content', '.post-content', '.entry-content', '#content', 'main'
    ];
    let bestEl = null; let bestScore = -Infinity;
    const calcScore = (el) => {
      let score = 0;
      const textLen = (el.textContent || '').length; score += Math.min(textLen / 800, 10);
      const pCount = el.querySelectorAll('p').length; score += pCount * 2;
      const aCount = el.querySelectorAll('a').length; const density = aCount / Math.max(pCount, 1); score -= density * 3;
      const cls = (el.className || '').toLowerCase(); const id = (el.id || '').toLowerCase();
      if (cls.includes('content') || id.includes('content')) score += 5;
      if (cls.includes('article') || id.includes('article')) score += 5;
      if (cls.includes('post') || id.includes('post')) score += 3;
      if (cls.includes('ad') || cls.includes('advertisement')) score -= 10;
      if (cls.includes('nav') || cls.includes('menu')) score -= 5;
      if (cls.includes('sidebar')) score -= 3;
      return score;
    }
    const candidates = new Set();
    selectors.forEach(sel => document.querySelectorAll(sel).forEach(el => candidates.add(el)));
    candidates.forEach(el => { const s = calcScore(el); if (s > bestScore) { bestScore = s; bestEl = el; } });

    // fallback：选择最长文本的section
    if (!bestEl) {
      document.querySelectorAll('section, div').forEach(el => {
        const t = (el.textContent || '').trim();
        if (t.split(/\s+/).length > 200) {
          const s = calcScore(el);
          if (s > bestScore) { bestScore = s; bestEl = el; }
        }
      });
    }

    if (!bestEl) return { title: document.title || '阅读模式', html: '<p>未能识别主要内容。</p>' };

    // 克隆并进行轻量清理
    const cleaned = bestEl.cloneNode(true);
    cleaned.querySelectorAll('script, style, noscript, iframe, object, embed, .advertisement, .ad, .social-share, .comments, .related-posts').forEach(el => el.remove());
    // 移除内联事件和多余属性
    const walker = document.createTreeWalker(cleaned, NodeFilter.SHOW_ELEMENT, null);
    let node; while (node = walker.nextNode()) {
      [...node.attributes].forEach(attr => {
        const name = attr.name.toLowerCase();
        if (!['href','src','alt','title','id','class'].includes(name)) node.removeAttribute(name);
        if (name.startsWith('on')) node.removeAttribute(name);
      });
    }

    return { title: document.title || '阅读模式', html: cleaned.innerHTML };
  }

  function openReader() {
    const container = ensureOverlay();
    const iframe = container.querySelector('iframe');
    const { title, html } = extractMainContent();
    toggleOverlay(true);

    const doc = iframe.contentDocument;
    // 应用用户设置
    getUserSettings().then(({ fontSize, theme, width }) => {
      const styles = doc.querySelector('style');
      const colors = (themeMap[theme] || themeMap.light);
      if (styles) {
        styles.textContent = styles.textContent.replace(
          /--bg-primary:.*?;/, `--bg-primary: ${colors.bg};`
        ).replace(
          /--bg-secondary:.*?;/, `--bg-secondary: ${colors.secondary};`
        ).replace(
          /--text-primary:.*?;/, `--text-primary: ${colors.text};`
        ).replace(
          /--text-secondary:.*?;/, `--text-secondary: ${colors.text === '#f9fafb' ? '#d1d5db' : '#6b7280'};`
        ).replace(
          /--border-color:.*?;/, `--border-color: ${colors.border};`
        );
      }
      const contentEl = doc.getElementById('reader-content');
      if (contentEl) {
        contentEl.style.fontSize = fontSize + 'px';
        contentEl.style.maxWidth = width + 'ch';
      }
    });

    if (!doc) return;
    const titleEl = doc.getElementById('reader-title');
    const contentEl = doc.getElementById('reader-content');
    const statusEl = doc.getElementById('reader-status');
    if (titleEl) titleEl.textContent = title;
    if (contentEl) contentEl.innerHTML = html;
    if (statusEl) statusEl.textContent = '内容已加载';

    // 从键盘ESC关闭
    window.addEventListener('keydown', escHandler, { once: true });
  }

  function closeReader() { toggleOverlay(false); }

  function escHandler(e) { if (e.key === 'Escape') closeReader(); }

  // 监听来自iframe的关闭事件
  window.addEventListener('message', (evt) => {
    if (evt?.data?.__reader_close) closeReader();
  });

  // 接收来自background/popup的消息
  chrome.runtime.onMessage.addListener((msg, _sender, _resp) => {
    if (msg?.type === 'TOGGLE_READER') {
      const container = document.getElementById(OVERLAY_ID);
      const visible = !!(container && container.style.display !== 'none');
      if (visible) closeReader(); else openReader();
    }
  });

  // 在页面加载后准备好容器
  ensureOverlay();
})();

