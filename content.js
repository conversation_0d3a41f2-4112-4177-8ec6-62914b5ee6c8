// content.js - 在页面中注入阅读器覆盖层，并进行启发式内容提取

(function () {
  // 改进的初始化检查：允许重新设置消息监听器，但避免重复创建DOM元素
  const OVERLAY_ID = 'reader-overlay-container';

  // 如果已经初始化过，强制重新设置消息监听器（解决首次点击问题）
  if (window.__READER_CONTENT_LOADED) {
    console.log('[阅读助手] 重新注入检测到，强制重新设置消息监听器');
    // 强制重新设置消息监听器，确保首次点击生效
    setupMessageListener();
    window.__READER_MESSAGE_LISTENER_SET = true;
    return;
  }
  window.__READER_CONTENT_LOADED = true;

  // 读取用户设置以应用到阅读器（顶层作用域，便于复用）
  async function getUserSettings() {
    return new Promise((resolve) => {
      try {
        chrome.storage?.local.get(['fontSize','theme','width'], (res) => {
          resolve({
            fontSize: res.fontSize ?? 16,
            theme: res.theme ?? 'light',
            width: res.width ?? 65,
          });
        });
      } catch (e) {
        resolve({ fontSize: 16, theme: 'light', width: 65 });
      }
    });
  }

  // 创建覆盖层（包含一个iframe以隔离样式）
  function ensureOverlay() {
    let container = document.getElementById(OVERLAY_ID);

    if (container) return container;

    container = document.createElement('div');
    container.id = OVERLAY_ID;

    const backdrop = document.createElement('div');
    backdrop.id = 'reader-overlay-backdrop';
    backdrop.addEventListener('click', () => toggleOverlay(false));

    const overlay = document.createElement('div');
    overlay.id = 'reader-overlay';

    const iframe = document.createElement('iframe');
    iframe.setAttribute('title', '阅读模式');
    iframe.setAttribute('aria-label', '阅读模式');

    overlay.appendChild(iframe);
    container.appendChild(backdrop);
    container.appendChild(overlay);

    document.documentElement.appendChild(container);

    // 初始化iframe文档
    iframe.addEventListener('load', () => {
      const doc = iframe.contentDocument;
      if (!doc) return;
      doc.open();
      doc.write(`<!doctype html><html lang="zh-CN"><head><meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>阅读模式</title>
        <style>
          :root {
            --bg-primary: #ffffff; --bg-secondary: #f9fafb; --text-primary: #1f2937; --text-secondary: #6b7280; --border-color: #e5e7eb;
          }
          html, body { height: auto; margin: 0; }
          body { background: var(--bg-primary); color: var(--text-primary); font-family: -apple-system,BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; overflow: hidden; }
          .reader-header { display:flex; justify-content:space-between; align-items:center; padding: 12px 20px; border-bottom: 1px solid var(--border-color); background: var(--bg-secondary); }
          .reader-title { margin: 0; font-size: 20px; font-weight: 600; }
          .reader-close { background: none; border: none; font-size: 22px; cursor: pointer; color: var(--text-secondary); padding: 4px 8px; border-radius: 6px; }
          .reader-close:hover { background: #f3f4f6; color: var(--text-primary); }
          .reader-content { height: auto; overflow: visible; padding: 24px; line-height: 1.7; max-width: 70ch; margin: 0 auto; }
          .reader-content img { max-width: 100%; height: auto; }
          .sr-only { position:absolute; width:1px; height:1px; padding:0; margin:-1px; overflow:hidden; clip:rect(0,0,0,0); border:0; }
        </style>
      </head><body>
        <header class="reader-header">
          <h1 class="reader-title" id="reader-title"></h1>
          <button class="reader-close" aria-label="关闭" title="关闭 (Esc)">×</button>
        </header>
        <main class="reader-content" role="main" id="reader-content"></main>
        <div class="sr-only" aria-live="polite" id="reader-status"></div>
      </body></html>`);
      doc.close();
    });

      // 立即绑定iframe内事件，不依赖load事件
      const bindIframeEvents = () => {
        try {
          const d = iframe.contentDocument;
          if (d && d.readyState === 'complete') {
            // 直接绑定关闭按钮事件（不使用内联脚本）
            const closeBtn = d.querySelector('.reader-close');
            if (closeBtn) {
              closeBtn.onclick = () => {
                window.postMessage({ type: 'READER_CLOSE' }, '*');
              };
            }

            // 绑定iframe内ESC键
            d.onkeydown = (e) => {
              if (e.key === 'Escape') {
                window.postMessage({ type: 'READER_CLOSE' }, '*');
              }
            };

            return true; // 绑定成功
          }
        } catch (e) {
          console.warn('绑定iframe事件失败:', e);
        }
        return false; // 绑定失败
      };

      // 多次尝试绑定，确保成功
      let bindAttempts = 0;
      const tryBind = () => {
        if (bindIframeEvents() || bindAttempts++ > 10) return;
        setTimeout(tryBind, 50);
      };
      setTimeout(tryBind, 50);

      // 优化的iframe高度自适应
      const setupIframeHeight = () => {
        try {
          const d = iframe.contentDocument;
          if (d && d.body) {
            const updateHeight = () => {
              // 确保iframe内部不产生滚动条
              const height = Math.max(
                d.body.scrollHeight,
                d.documentElement.scrollHeight,
                window.innerHeight // 至少等于视口高度
              );
              iframe.style.height = height + 'px';

              // 确保iframe内部元素不会产生滚动
              d.documentElement.style.overflow = 'hidden';
              d.body.style.overflow = 'hidden';
            };

            updateHeight();

            // 监听内容和窗口大小变化
            const observer = new MutationObserver(updateHeight);
            observer.observe(d.body, { childList: true, subtree: true, attributes: true });
            window.addEventListener('resize', updateHeight);

            // 存储清理函数
            iframe.__heightCleanup = () => {
              observer.disconnect();
              window.removeEventListener('resize', updateHeight);
            };
          }
        } catch (e) {
          console.warn('设置iframe高度失败:', e);
        }
      };

      setTimeout(setupIframeHeight, 100);

    return container;
  }

  function toggleOverlay(show) {
    const container = ensureOverlay();
    container.style.display = show ? 'block' : 'none';

    // 显示覆盖层时隐藏原网页滚动条，关闭时恢复
    if (show) {
      // 保存原始overflow值
      if (!document.body.__originalOverflow) {
        document.body.__originalOverflow = document.body.style.overflow || '';
        document.documentElement.__originalOverflow = document.documentElement.style.overflow || '';
      }
      // 隐藏滚动条
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
    } else {
      // 恢复原始overflow值
      if (document.body.__originalOverflow !== undefined) {
        document.body.style.overflow = document.body.__originalOverflow;
        document.documentElement.style.overflow = document.documentElement.__originalOverflow;
        delete document.body.__originalOverflow;
        delete document.documentElement.__originalOverflow;
      }
    }
  }

    // 应用主题（简化实现）
    const themeMap = {
      light: { bg: '#ffffff', text: '#1f2937', border: '#e5e7eb', secondary: '#f9fafb' },
      dark: { bg: '#1f2937', text: '#f9fafb', border: '#4b5563', secondary: '#374151' },
      sepia: { bg: '#f7f3e9', text: '#5d4e37', border: '#d4c4a8', secondary: '#f0e6d2' },
    };

  // 增强启发式：正文候选评分 + 黑/白名单 + 结构特征
  function extractMainContent() {
    const POSITIVE_CLASS_HINTS = ['content','article','post','entry','main','page','text','body'];
    const NEGATIVE_CLASS_HINTS = ['comment','meta','footer','footnote','sidebar','nav','menu','header','advert','ad','sponsor','promo','share','related','popup','modal'];
    const SELECTORS = ['article','main','[role="main"]','.content','.post-content','.entry-content','#content','.article-body','.post-body'];

    // 预定义噪声选择器（仅用于评分与清理，不修改原页面DOM）
    const NOISE_SELECTORS = ['script','style','noscript','iframe','object','embed','form','.advertisement','.ad','.social-share','.related-posts','aside','.footer','.header','.nav'];

    // 候选集合：包含可见的块级容器
    const candidates = new Set();
    const pushCandidate = (el) => {
      if (!el) return;
      const rect = el.getBoundingClientRect?.();
      if (rect && (rect.width < 200 || rect.height < 50)) return; // 过滤过小容器
      if (window.getComputedStyle(el).display === 'none') return;  // 过滤隐藏
      candidates.add(el);
    };

    SELECTORS.forEach(sel => document.querySelectorAll(sel).forEach(pushCandidate));
    if (candidates.size === 0) {
      document.querySelectorAll('section, article, main, div').forEach(pushCandidate);
    }

    const scoreEl = (el) => {
      let score = 0;
      const text = (el.textContent || '').replace(/\s+/g, ' ').trim();
      const length = text.length;
      const pCount = el.querySelectorAll('p').length;
      const imgCount = el.querySelectorAll('img').length;
      const linkCount = el.querySelectorAll('a').length;

      // 基础文本与段落权重
      score += Math.min(length / 1000, 15);
      score += Math.min(pCount * 1.5, 15);

      // 链接密度惩罚（评论区通常链接密度高）
      const linkDensity = linkCount / Math.max(pCount, 1);
      score -= Math.min(linkDensity * 4, 12);

      // 图片适度奖励
      if (imgCount > 0 && imgCount < pCount * 0.5) score += 2;

      // 类名/ID 语义判断
      const cls = (el.className || '').toLowerCase();
      const id = (el.id || '').toLowerCase();
      if (POSITIVE_CLASS_HINTS.some(k => cls.includes(k) || id.includes(k))) score += 12;
      if (NEGATIVE_CLASS_HINTS.some(k => cls.includes(k) || id.includes(k))) score -= 18;

      // 评论区特征识别（新增）
      if (/comment|reply|discuss|feedback|review/.test(cls + ' ' + id)) score -= 20;

      // 广告与推广内容识别
      const html = (el.innerHTML || '').toLowerCase();
      if (/sponsored|advert|promo|affiliate/.test(html)) score -= 12;

      // 视觉特征判断（新增核心功能）
      try {
        const style = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect?.();

        if (rect && style) {
          // 位置特征：过于靠顶部/底部的可能是导航/页脚
          if (rect.top < 100) score -= 3;
          if (rect.bottom > window.innerHeight * 0.9) score -= 2;

          // 字体大小特征：正文通常字体适中
          const fontSize = parseFloat(style.fontSize);
          if (fontSize >= 14 && fontSize <= 18) score += 3;
          if (fontSize < 12) score -= 5; // 过小可能是版权/注释
          if (fontSize > 24) score -= 2; // 过大可能是标题容器

          // 宽度特征：过窄的可能是侧边栏
          if (rect.width < 300) score -= 5;
          if (rect.width > window.innerWidth * 0.6) score += 2;

          // 颜色对比度：正文通常有良好对比度
          const color = style.color;
          const bgColor = style.backgroundColor;
          if (color && color !== 'rgba(0, 0, 0, 0)') {
            // 简单的对比度检查
            if (color.includes('rgb(') && !color.includes('rgba(')) score += 1;
          }

          // 行高特征：正文通常有合理行高
          const lineHeight = parseFloat(style.lineHeight);
          if (lineHeight >= 1.4 && lineHeight <= 1.8) score += 2;
        }
      } catch (e) {
        // 视觉特征获取失败不影响基础评分
      }

      // 内容质量特征（新增）
      const sentences = text.split(/[.!?。！？]/).filter(s => s.trim().length > 10);
      if (sentences.length >= 3) score += 3; // 有完整句子的内容

      // 段落平均长度（正文段落通常较长）
      const avgParagraphLength = pCount > 0 ? length / pCount : 0;
      if (avgParagraphLength > 100) score += 2;
      if (avgParagraphLength < 30) score -= 3;

      return score;
    };

    let bestEl = null; let bestScore = -Infinity;
    candidates.forEach(el => {
      const s = scoreEl(el);
      if (s > bestScore) { bestScore = s; bestEl = el; }
    });

    // 失败回退：从最长段落拼装
    if (!bestEl || bestScore < 5) {
      const paragraphs = Array.from(document.querySelectorAll('p')).filter(p => (p.textContent || '').trim().length > 80);
      if (paragraphs.length > 3) {
        const container = document.createElement('div');
        paragraphs.slice(0, 30).forEach(p => container.appendChild(p.cloneNode(true)));
        return { title: document.title || '阅读模式', html: container.innerHTML };
      }
    }

    if (!bestEl) return { title: document.title || '阅读模式', html: '<p>未能识别主要内容。</p>' };

    // 深度清理克隆（仅对克隆内容动手）
    const cleaned = bestEl.cloneNode(true);
    cleaned.querySelectorAll(NOISE_SELECTORS.join(',')).forEach(n => n.remove());
    cleaned.querySelectorAll('aside, nav, form, footer, header').forEach(n => n.remove());

    // 移除内联事件与多余属性
    const walker = document.createTreeWalker(cleaned, NodeFilter.SHOW_ELEMENT, null);
    let node; while (node = walker.nextNode()) {
      [...node.attributes].forEach(attr => {
        const name = attr.name.toLowerCase();
        if (!['href','src','alt','title','id','class'].includes(name)) node.removeAttribute(name);
        if (name.startsWith('on')) node.removeAttribute(name);
      });
    }

    // 删除明显短小/链接密度高的段落，提高纯度
    cleaned.querySelectorAll('p').forEach(p => {
      const t = (p.textContent || '').trim();
      const links = p.querySelectorAll('a').length;
      if (t.length < 40 && links > 0) p.remove();

    });

    return { title: document.title || '阅读模式', html: cleaned.innerHTML };
  }

  function openReader() {
    const container = ensureOverlay();
    const iframe = container.querySelector('iframe');
    const { title, html } = extractMainContent();

    // 先设置内容，再显示，避免闪烁
    const doc = iframe.contentDocument;
    if (!doc) return;
    const titleEl = doc.getElementById('reader-title');
    const contentEl = doc.getElementById('reader-content');
    const statusEl = doc.getElementById('reader-status');
    if (titleEl) titleEl.textContent = title;
    if (contentEl) contentEl.innerHTML = html;
    if (statusEl) statusEl.textContent = '内容已加载';

    // 应用用户设置
    getUserSettings().then(({ fontSize, theme, width }) => {
      const styles = doc.querySelector('style');
      const colors = (themeMap[theme] || themeMap.light);
      if (styles) {
        styles.textContent = styles.textContent.replace(
          /--bg-primary:.*?;/, `--bg-primary: ${colors.bg};`
        ).replace(
          /--bg-secondary:.*?;/, `--bg-secondary: ${colors.secondary};`
        ).replace(
          /--text-primary:.*?;/, `--text-primary: ${colors.text};`
        ).replace(
          /--text-secondary:.*?;/, `--text-secondary: ${colors.text === '#f9fafb' ? '#d1d5db' : '#6b7280'};`
        ).replace(
          /--border-color:.*?;/, `--border-color: ${colors.border};`
        );
      }
      const contentEl2 = doc.getElementById('reader-content');
      if (contentEl2) {
        contentEl2.style.fontSize = fontSize + 'px';
        contentEl2.style.maxWidth = width + 'ch';
      }

      // 设置完成后再显示
      toggleOverlay(true);

      // 内容加载后重新计算iframe高度
      setTimeout(() => {
        try {
          const d = iframe.contentDocument;
          if (d && d.body) {
            // 强制重新计算高度
            const height = Math.max(
              d.body.scrollHeight,
              d.documentElement.scrollHeight,
              window.innerHeight
            );
            iframe.style.height = height + 'px';

            // 确保iframe内部不滚动
            d.documentElement.style.overflow = 'hidden';
            d.body.style.overflow = 'hidden';
          }
        } catch (e) {
          console.warn('更新iframe高度失败:', e);
        }
      }, 200);
    });

    // 父文档 ESC 关闭
    window.addEventListener('keydown', escHandler, { once: true });
  }

  function closeReader() {
    toggleOverlay(false);
    // 清理ESC监听器
    window.removeEventListener('keydown', escHandler);

    // 清理iframe高度监听器
    const container = document.getElementById(OVERLAY_ID);
    if (container) {
      const iframe = container.querySelector('iframe');
      if (iframe && iframe.__heightCleanup) {
        iframe.__heightCleanup();
      }
    }
  }

  // 父文档ESC关闭，保证无论焦点在何处都可关闭
  function escHandler(e) {
    if (e.key === 'Escape') {
      closeReader();
    }
  }

  // 监听来自iframe的关闭消息
  window.addEventListener('message', (evt) => {
    if (evt?.data?.type === 'READER_CLOSE') {
      closeReader();
    }
  });

  // 设置消息监听器的函数，可重复调用
  function setupMessageListener() {
    // 移除可能存在的旧监听器
    if (window.__READER_MESSAGE_LISTENER) {
      try {
        chrome.runtime.onMessage.removeListener(window.__READER_MESSAGE_LISTENER);
        console.log('[阅读助手] 已移除旧的消息监听器');
      } catch (e) {
        console.warn('[阅读助手] 移除旧监听器失败:', e);
      }
    }

    // 创建新的消息监听器
    window.__READER_MESSAGE_LISTENER = (msg, _sender, sendResponse) => {
      console.log('[阅读助手] 收到消息:', msg);

      if (msg?.type === 'TOGGLE_READER') {
        try {
          const container = document.getElementById(OVERLAY_ID);
          const visible = !!(container && container.style.display !== 'none');
          console.log('[阅读助手] 当前状态:', visible ? '已显示' : '已隐藏');

          if (visible) {
            closeReader();
          } else {
            openReader();
          }
          sendResponse({ success: true, action: visible ? 'closed' : 'opened' });
        } catch (e) {
          console.error('[阅读助手] 切换阅读模式失败:', e);
          sendResponse({ success: false, error: e.message });
        }
      }
      return true; // 保持消息通道开放
    };

    chrome.runtime.onMessage.addListener(window.__READER_MESSAGE_LISTENER);
    console.log('[阅读助手] 消息监听器已设置');
  }

  // 初始化：设置消息监听器并准备容器
  setupMessageListener();
  window.__READER_MESSAGE_LISTENER_SET = true;
  ensureOverlay();
})();

