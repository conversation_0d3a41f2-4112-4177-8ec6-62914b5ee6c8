// content.js - 在页面中注入阅读器覆盖层，并进行启发式内容提取

(function () {
  // 幂等守卫，避免重复初始化
  if (window.__READER_CONTENT_LOADED) return;
  window.__READER_CONTENT_LOADED = true;

  const OVERLAY_ID = 'reader-overlay-container';

  // 读取用户设置以应用到阅读器（顶层作用域，便于复用）
  async function getUserSettings() {
    return new Promise((resolve) => {
      try {
        chrome.storage?.local.get(['fontSize','theme','width'], (res) => {
          resolve({
            fontSize: res.fontSize ?? 16,
            theme: res.theme ?? 'light',
            width: res.width ?? 65,
          });
        });
      } catch (e) {
        resolve({ fontSize: 16, theme: 'light', width: 65 });
      }
    });
  }

  // 创建覆盖层（包含一个iframe以隔离样式）
  function ensureOverlay() {
    let container = document.getElementById(OVERLAY_ID);

    if (container) return container;

    container = document.createElement('div');
    container.id = OVERLAY_ID;

    const backdrop = document.createElement('div');
    backdrop.id = 'reader-overlay-backdrop';
    backdrop.addEventListener('click', () => toggleOverlay(false));

    const overlay = document.createElement('div');
    overlay.id = 'reader-overlay';

    const iframe = document.createElement('iframe');
    iframe.setAttribute('title', '阅读模式');
    iframe.setAttribute('aria-label', '阅读模式');

    overlay.appendChild(iframe);
    container.appendChild(backdrop);
    container.appendChild(overlay);

    document.documentElement.appendChild(container);

    // 初始化iframe文档
    iframe.addEventListener('load', () => {
      const doc = iframe.contentDocument;
      if (!doc) return;
      doc.open();
      doc.write(`<!doctype html><html lang="zh-CN"><head><meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>阅读模式</title>
        <style>
          :root {
            --bg-primary: #ffffff; --bg-secondary: #f9fafb; --text-primary: #1f2937; --text-secondary: #6b7280; --border-color: #e5e7eb;
          }
          html, body { height: auto; margin: 0; }
          body { background: var(--bg-primary); color: var(--text-primary); font-family: -apple-system,BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; overflow: hidden; }
          .reader-header { display:flex; justify-content:space-between; align-items:center; padding: 12px 20px; border-bottom: 1px solid var(--border-color); background: var(--bg-secondary); }
          .reader-title { margin: 0; font-size: 20px; font-weight: 600; }
          .reader-close { background: none; border: none; font-size: 22px; cursor: pointer; color: var(--text-secondary); padding: 4px 8px; border-radius: 6px; }
          .reader-close:hover { background: #f3f4f6; color: var(--text-primary); }
          .reader-content { height: auto; overflow: visible; padding: 24px; line-height: 1.7; max-width: 70ch; margin: 0 auto; }
          .reader-content img { max-width: 100%; height: auto; }
          .sr-only { position:absolute; width:1px; height:1px; padding:0; margin:-1px; overflow:hidden; clip:rect(0,0,0,0); border:0; }
        </style>
      </head><body>
        <header class="reader-header">
          <h1 class="reader-title" id="reader-title"></h1>
          <button class="reader-close" aria-label="关闭" title="关闭 (Esc)">×</button>
        </header>
        <main class="reader-content" role="main" id="reader-content"></main>
        <div class="sr-only" aria-live="polite" id="reader-status"></div>
      </body></html>`);
      doc.close();
    });

      // 重新添加内联脚本，使用postMessage通信（更可靠）
      setTimeout(() => {
        try {
          const d = iframe.contentDocument;
          if (d) {
            const script = d.createElement('script');
            script.textContent = `
              // 绑定关闭按钮
              const closeBtn = document.querySelector('.reader-close');
              if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                  parent.postMessage({ type: 'READER_CLOSE' }, '*');
                });
              }
              // 绑定ESC键
              document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                  parent.postMessage({ type: 'READER_CLOSE' }, '*');
                }
              });
            `;
            d.head.appendChild(script);
          }
        } catch (e) {
          console.warn('无法绑定iframe内事件:', e);
        }
      }, 100);

      // 自适应iframe高度，确保滚动正常工作
      setTimeout(() => {
        try {
          const d = iframe.contentDocument;
          if (d && d.body) {
            const updateHeight = () => {
              const height = Math.max(
                d.body.scrollHeight,
                d.body.offsetHeight,
                d.documentElement.clientHeight,
                d.documentElement.scrollHeight,
                d.documentElement.offsetHeight
              );
              iframe.style.height = height + 'px';
            };
            updateHeight();
            // 监听内容变化，重新计算高度
            const observer = new MutationObserver(updateHeight);
            observer.observe(d.body, { childList: true, subtree: true });
          }
        } catch (e) {
          console.warn('无法设置iframe高度:', e);
        }
      }, 200);

    return container;
  }

  function toggleOverlay(show) {
    const container = ensureOverlay();
    container.style.display = show ? 'block' : 'none';
  }

    // 应用主题（简化实现）
    const themeMap = {
      light: { bg: '#ffffff', text: '#1f2937', border: '#e5e7eb', secondary: '#f9fafb' },
      dark: { bg: '#1f2937', text: '#f9fafb', border: '#4b5563', secondary: '#374151' },
      sepia: { bg: '#f7f3e9', text: '#5d4e37', border: '#d4c4a8', secondary: '#f0e6d2' },
    };

  // 增强启发式：正文候选评分 + 黑/白名单 + 结构特征
  function extractMainContent() {
    const POSITIVE_CLASS_HINTS = ['content','article','post','entry','main','page','text','body'];
    const NEGATIVE_CLASS_HINTS = ['comment','meta','footer','footnote','sidebar','nav','menu','header','advert','ad','sponsor','promo','share','related','popup','modal'];
    const SELECTORS = ['article','main','[role="main"]','.content','.post-content','.entry-content','#content','.article-body','.post-body'];

    // 预定义噪声选择器（仅用于评分与清理，不修改原页面DOM）
    const NOISE_SELECTORS = ['script','style','noscript','iframe','object','embed','form','.advertisement','.ad','.social-share','.related-posts','aside','.footer','.header','.nav'];

    // 候选集合：包含可见的块级容器
    const candidates = new Set();
    const pushCandidate = (el) => {
      if (!el) return;
      const rect = el.getBoundingClientRect?.();
      if (rect && (rect.width < 200 || rect.height < 50)) return; // 过滤过小容器
      if (window.getComputedStyle(el).display === 'none') return;  // 过滤隐藏
      candidates.add(el);
    };

    SELECTORS.forEach(sel => document.querySelectorAll(sel).forEach(pushCandidate));
    if (candidates.size === 0) {
      document.querySelectorAll('section, article, main, div').forEach(pushCandidate);
    }

    const scoreEl = (el) => {
      let score = 0;
      const text = (el.textContent || '').replace(/\s+/g, ' ').trim();
      const length = text.length;
      const pCount = el.querySelectorAll('p').length;
      const imgCount = el.querySelectorAll('img').length;
      const linkCount = el.querySelectorAll('a').length;

      // 基础文本与段落
      score += Math.min(length / 1000, 15);
      score += Math.min(pCount * 1.5, 15);

      // 链接密度（惩罚）
      const linkDensity = linkCount / Math.max(pCount, 1);
      score -= Math.min(linkDensity * 3, 10);

      // 图片适度奖励（正文常含少量图片）
      if (imgCount > 0 && imgCount < pCount * 0.5) score += 2;

      // 类名/ID 命中与否
      const cls = (el.className || '').toLowerCase();
      const id = (el.id || '').toLowerCase();
      if (POSITIVE_CLASS_HINTS.some(k => cls.includes(k) || id.includes(k))) score += 10;
      if (NEGATIVE_CLASS_HINTS.some(k => cls.includes(k) || id.includes(k))) score -= 15;

      // 广告标识惩罚
      const html = (el.innerHTML || '').toLowerCase();
      if (/sponsored|advert|promo/.test(html)) score -= 10;

      // 结构位置：过于靠顶部的大容器可能是布局容器
      const rect = el.getBoundingClientRect?.();
      if (rect && rect.top < 100) score -= 2;

      return score;
    };

    let bestEl = null; let bestScore = -Infinity;
    candidates.forEach(el => {
      const s = scoreEl(el);
      if (s > bestScore) { bestScore = s; bestEl = el; }
    });

    // 失败回退：从最长段落拼装
    if (!bestEl || bestScore < 5) {
      const paragraphs = Array.from(document.querySelectorAll('p')).filter(p => (p.textContent || '').trim().length > 80);
      if (paragraphs.length > 3) {
        const container = document.createElement('div');
        paragraphs.slice(0, 30).forEach(p => container.appendChild(p.cloneNode(true)));
        return { title: document.title || '阅读模式', html: container.innerHTML };
      }
    }

    if (!bestEl) return { title: document.title || '阅读模式', html: '<p>未能识别主要内容。</p>' };

    // 深度清理克隆（仅对克隆内容动手）
    const cleaned = bestEl.cloneNode(true);
    cleaned.querySelectorAll(NOISE_SELECTORS.join(',')).forEach(n => n.remove());
    cleaned.querySelectorAll('aside, nav, form, footer, header').forEach(n => n.remove());

    // 移除内联事件与多余属性
    const walker = document.createTreeWalker(cleaned, NodeFilter.SHOW_ELEMENT, null);
    let node; while (node = walker.nextNode()) {
      [...node.attributes].forEach(attr => {
        const name = attr.name.toLowerCase();
        if (!['href','src','alt','title','id','class'].includes(name)) node.removeAttribute(name);
        if (name.startsWith('on')) node.removeAttribute(name);
      });
    }

    // 删除明显短小/链接密度高的段落，提高纯度
    cleaned.querySelectorAll('p').forEach(p => {
      const t = (p.textContent || '').trim();
      const links = p.querySelectorAll('a').length;
      if (t.length < 40 && links > 0) p.remove();

    });

    return { title: document.title || '阅读模式', html: cleaned.innerHTML };
  }

  function openReader() {
    const container = ensureOverlay();
    const iframe = container.querySelector('iframe');
    const { title, html } = extractMainContent();

    // 先设置内容，再显示，避免闪烁
    const doc = iframe.contentDocument;
    if (!doc) return;
    const titleEl = doc.getElementById('reader-title');
    const contentEl = doc.getElementById('reader-content');
    const statusEl = doc.getElementById('reader-status');
    if (titleEl) titleEl.textContent = title;
    if (contentEl) contentEl.innerHTML = html;
    if (statusEl) statusEl.textContent = '内容已加载';

    // 应用用户设置
    getUserSettings().then(({ fontSize, theme, width }) => {
      const styles = doc.querySelector('style');
      const colors = (themeMap[theme] || themeMap.light);
      if (styles) {
        styles.textContent = styles.textContent.replace(
          /--bg-primary:.*?;/, `--bg-primary: ${colors.bg};`
        ).replace(
          /--bg-secondary:.*?;/, `--bg-secondary: ${colors.secondary};`
        ).replace(
          /--text-primary:.*?;/, `--text-primary: ${colors.text};`
        ).replace(
          /--text-secondary:.*?;/, `--text-secondary: ${colors.text === '#f9fafb' ? '#d1d5db' : '#6b7280'};`
        ).replace(
          /--border-color:.*?;/, `--border-color: ${colors.border};`
        );
      }
      const contentEl2 = doc.getElementById('reader-content');
      if (contentEl2) {
        contentEl2.style.fontSize = fontSize + 'px';
        contentEl2.style.maxWidth = width + 'ch';
      }

      // 设置完成后再显示
      toggleOverlay(true);

      // 确保iframe高度正确
      setTimeout(() => {
        try {
          const d = iframe.contentDocument;
          if (d && d.body) {
            const height = Math.max(
              d.body.scrollHeight,
              d.body.offsetHeight,
              d.documentElement.clientHeight,
              d.documentElement.scrollHeight,
              d.documentElement.offsetHeight
            );
            iframe.style.height = height + 'px';
          }
        } catch (e) {
          console.warn('无法更新iframe高度:', e);
        }
      }, 300);
    });

    // 父文档 ESC 关闭
    window.addEventListener('keydown', escHandler, { once: true });
  }

  function closeReader() {
    toggleOverlay(false);
    // 移除ESC监听器，避免累积
    window.removeEventListener('keydown', escHandler);
  }

  // 父文档ESC关闭，保证无论焦点在何处都可关闭
  function escHandler(e) {
    if (e.key === 'Escape') {
      closeReader();
    }
  }

  // 监听来自iframe的关闭消息
  window.addEventListener('message', (evt) => {
    if (evt?.data?.type === 'READER_CLOSE') {
      closeReader();
    }
  });

  // 接收来自background/popup的消息
  chrome.runtime.onMessage.addListener((msg, _sender, sendResponse) => {
    if (msg?.type === 'TOGGLE_READER') {
      try {
        const container = document.getElementById(OVERLAY_ID);
        const visible = !!(container && container.style.display !== 'none');
        if (visible) {
          closeReader();
        } else {
          openReader();
        }
        sendResponse({ success: true });
      } catch (e) {
        console.warn('切换阅读模式失败:', e);
        sendResponse({ success: false, error: e.message });
      }
    }
    return true; // 保持消息通道开放
  });

  // 在页面加载后准备好容器
  ensureOverlay();
})();

