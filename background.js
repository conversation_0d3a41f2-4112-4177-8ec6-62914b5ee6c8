// background.js (service worker)
// 负责和popup、content script通信，协调提取与渲染流程

chrome.runtime.onInstalled.addListener(() => {
  console.log('[阅读助手] 扩展已安装');
});

chrome.action.onClicked?.addListener(async (tab) => {
  if (!tab.id) return;
  // 后备触发：如果用户点击扩展图标且没有popup时，向content触发切换
  await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' }).catch(() => {});
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message?.type === 'GET_ACTIVE_TAB') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      sendResponse(tabs?.[0]);
    });
    return true; // 异步响应
  }
});

