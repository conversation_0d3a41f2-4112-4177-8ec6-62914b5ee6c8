/* content.css - MVP 阅读器样式，尽量简洁，后续可替换为Tailwind/设计系统 */

#reader-overlay-container {
  position: fixed;
  inset: 0;
  z-index: 2147483647;
  display: none; /* 通过JS切换显示 */
}

#reader-overlay-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.08);
}

#reader-overlay iframe {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
}

/* 读者iframe内基础样式（通过内联注入）参考UX文档的基础排版比例 */

