/* content.css - MVP 阅读器样式，尽量简洁，后续可替换为Tailwind/设计系统 */

#reader-overlay-container {
  position: fixed;
  inset: 0;
  z-index: 2147483647;
  display: none; /* 通过JS切换显示 */
}

#reader-overlay-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.08);
}

#reader-overlay {
  position: absolute;
  inset: 0;
  overflow: auto; /* 覆盖层承载滚动 */
}

#reader-overlay iframe {
  position: relative;
  width: 100%;
  height: auto; /* 改为auto，由JS动态设置具体高度 */
  min-height: 100vh; /* 最小高度为视口高度 */
  border: none;
  background: #fff;
  display: block;
}

/* 读者iframe内基础样式（通过内联注入）参考UX文档的基础排版比例 */

