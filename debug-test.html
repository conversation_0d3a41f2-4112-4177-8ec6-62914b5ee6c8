<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读助手调试测试页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            margin: 20px; 
            height: 200vh; /* 确保页面有滚动条 */
        }
        .test-section { 
            background: #f0f0f0; 
            padding: 20px; 
            margin: 20px 0; 
            border: 1px solid #ccc;
        }
        .scroll-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator">
        滚动条测试指示器 - 开启阅读模式时应该看不到这个和页面滚动条
    </div>

    <div class="test-section">
        <h1>首次点击测试</h1>
        <p><strong>测试步骤：</strong></p>
        <ol>
            <li>在新标签页打开这个页面</li>
            <li>第一次点击扩展图标中的"开启/关闭阅读模式"按钮</li>
            <li>应该立即开启阅读模式，无需点击第二次</li>
        </ol>
    </div>

    <div class="test-section">
        <h1>滚动条隐藏测试</h1>
        <p><strong>测试步骤：</strong></p>
        <ol>
            <li>注意当前页面右侧有滚动条</li>
            <li>开启阅读模式后，原页面的滚动条应该完全不可见</li>
            <li>右上角的"滚动条测试指示器"也应该被覆盖层遮盖</li>
        </ol>
    </div>

    <article class="content">
        <h1>测试文章标题</h1>
        <p>这是测试文章的第一段内容。这段内容应该被正确提取到阅读模式中。当开启阅读模式时，原网页的所有UI元素（包括滚动条和右上角的指示器）都应该完全不可见。</p>
        
        <p>这是测试文章的第二段内容。我们需要验证首次点击能够立即生效，不需要点击第二次。这个问题已经通过强制重新设置消息监听器和改进重试机制来解决。</p>
        
        <h2>关闭功能测试</h2>
        <p>关闭功能已经修复成功。可以通过以下方式关闭阅读模式：</p>
        <ul>
            <li>点击右上角的关闭按钮(×)</li>
            <li>按键盘ESC键</li>
            <li>点击背景遮罩区域</li>
        </ul>
        
        <h2>滚动功能测试</h2>
        <p>在阅读模式中，应该只有一个滚动条，不会出现双重滚动条的问题。滚动应该是流畅的，内容会跟随滚动条移动。</p>
        
        <p>这是更多的测试内容，用来确保页面有足够的长度来测试滚动功能。当内容超过视口高度时，应该能够正常滚动浏览所有内容。</p>
        
        <p>最后一段测试内容。所有的修复都应该正常工作：首次点击立即生效、原网页滚动条完全隐藏、关闭功能正常、滚动体验流畅。</p>
    </article>

    <div style="height: 50vh; background: #e0e0e0; padding: 20px; margin-top: 30px;">
        <h3>页面底部内容</h3>
        <p>这是页面底部的内容，用来确保页面有足够的高度产生滚动条。</p>
    </div>

    <script>
        // 添加一些调试信息
        console.log('[调试页面] 页面已加载，当前URL:', window.location.href);
        console.log('[调试页面] 页面高度:', document.body.scrollHeight);
        console.log('[调试页面] 视口高度:', window.innerHeight);
        
        // 监听滚动事件，验证滚动条是否正常
        window.addEventListener('scroll', () => {
            console.log('[调试页面] 页面滚动位置:', window.scrollY);
        });
    </script>
</body>
</html>
